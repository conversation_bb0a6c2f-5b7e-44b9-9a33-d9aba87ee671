Feature: Match Group

  Background:
    Given The user is on Event Screen

  @e2e @match-group
  Scenario: Check UI of Match Group
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to match group event details page
    When The user clicks on the "Match Groups" tab
    And The user clicks "View Match Group" for the first match group
    Then The user should see the match group page UI
    And The user should see the following match group UI elements:
      | Element Type       | Expected Value                     |
      | Breadcrumb         | lucy                               |
      | File Name Dropdown | visible                            |
      | Export Button      | visible                            |
      | Left Panel Text    | Select a Detection to View Details |
      | Tab 1              | Search Results                     |
      | Tab 2              | Verified Matches                   |
      | Tab 3              | Timeline Editor                    |
      | Default Tab        | Verified Matches                   |

  @e2e @match-group
  Scenario: Verify user can change name for match group search
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to match group event details page
    When The user clicks on the "Match Groups" tab
    And The user expands the first match group
    When The user hovers on a match group search
    And The user clicks on the edit icon for the search
    And The user enters "e2e-updated-search-name" as the new search name
    And The user clicks "Save" in the search dialog
    Then The search name should be updated to "e2e-updated-search-name"

  @e2e @match-group
  Scenario: Verify user can delete a match group search
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to match group event details page
    When The user clicks on the "Match Groups" tab
    And The user expands the first match group
    When The user hovers on a match group search
    And The user clicks on the delete icon for the search
    And The user clicks "Delete" in the search dialog
    Then The match group search should be deleted successfully

  @e2e @match-group
  Scenario: Verify user can edit match group name
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to match group event details page
    When The user clicks on the "Match Groups" tab
    When The user clicks on the 3 dots button for the first match group
    And The user selects "Rename"
    And The user enters "e2e-updated-match-group" as the new name
    And The user clicks "Save" in the match group dialog
    Then The match group name should be updated to "e2e-updated-match-group"

  @e2e @match-group
  Scenario: Verify user can delete match group
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to match group event details page
    When The user clicks on the "Match Groups" tab
    When The user clicks on the 3 dots button for the first match group
    And The user selects "Delete"
    Then The user should see the delete confirmation modal
    And The confirmation modal should display the message "Are you sure you want to delete this match group? This will remove all searches and timelines associated to it."
    And The modal should have "Cancel" and "Yes, Delete" buttons
    When The user clicks "Yes, Delete"
    Then The match group should be deleted successfully
