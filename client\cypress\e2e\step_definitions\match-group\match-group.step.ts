import { Before, Then, When, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { matchGroupPage } from '../../../pages/matchGroupPage';
import { eventDetailsPage } from '../../../pages/eventDetailsPage';
import { mainPage } from '../../../pages/mainPage';
import { MatchGroupButton, MatchGroupTab } from '../../../support/helperFunction/matchGroupHelper';
import '../activeTab/activeTab.step';
import '../main-page/main-page.steps';

Before({ tags: '@match-group' }, () => {
  cy.LoginLandingPage();
});

When('The user clicks {string} for the first match group', (action: string) => {
  if (action === MatchGroupButton.VIEW_MATCH_GROUP) {
    cy.get('[data-testid^="match-group-row-"]').first().within(() => {
      cy.contains('button', MatchGroupButton.VIEW_MATCH_GROUP).click();
    });
  }
});

Then('The user should see the match group page UI', () => {
  matchGroupPage.verifyMatchGroupPageUI();
});

Then('The user should see the breadcrumb with event name {string} and group name', (eventName: string) => {
  cy.get('[data-testid="match-group-breadcrumb"]').invoke('text').then((groupName: string) => {
    matchGroupPage.verifyBreadcrumb(eventName, groupName);
  });
});

Then('The user should see the File Name dropdown and Export button', () => {
  matchGroupPage.verifyFileNameDropdownAndExportButton();
});

Then('The user should see {string} on the left screen', (text: string) => {
  if (text === 'Select a Detection to View Details') {
    matchGroupPage.verifyLeftScreenContent();
  }
});

Then('The user should see the right screen tabs {string}, {string}, and {string}', (_tab1: string, _tab2: string, _tab3: string) => {
  matchGroupPage.verifyRightScreenTabs();
});

Then('The {string} tab should be selected by default', (tabName: string) => {
  if (tabName === MatchGroupTab.VERIFIED_MATCHES) {
    matchGroupPage.verifyDefaultTab();
  }
});

// Match Group Page specific steps - using more specific step text to avoid conflicts
When('The user hovers on a match group search in the match group page', () => {
  matchGroupPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search in the match group page', () => {
  matchGroupPage.clickFirstEditIcon();
});

When('The user enters {string} as the new search name in the match group page', (newName: string) => {
  matchGroupPage.enterNewSearchName(newName);
});

When('The user clicks {string} in the match group page dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    matchGroupPage.clickSaveInDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    matchGroupPage.clickDeleteInDialog();
  }
});

Then('The search name should be updated to {string} in the match group page', (newName: string) => {
  matchGroupPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search in the match group page', () => {
  matchGroupPage.clickFirstDeleteIcon();
});

Then('The match group search should be deleted successfully in the match group page', () => {
  cy.get('[data-testid^="match-group-row-"]').should('have.length.lessThan', 2);
});

When('The user clicks on the 3 dots button in the match group page', () => {
  matchGroupPage.clickThreeDotsButton();
});

When('The user selects {string} from the match group page menu', (option: string) => {
  if (option === 'Rename') {
    matchGroupPage.selectRename();
  } else if (option === 'Delete') {
    matchGroupPage.selectDelete();
  }
});

When('The user enters {string} as the new match group name in the match group page', (newName: string) => {
  matchGroupPage.enterNewMatchGroupName(newName);
});

Then('The match group name should be updated to {string} in the match group page', (newName: string) => {
  matchGroupPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The confirmation modal should display the delete message in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The modal should have Cancel and Delete buttons in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

When('The user clicks {string} in the match group page confirmation', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    matchGroupPage.clickYesDelete();
  }
});

Then('The match group should be deleted successfully from the match group page', () => {
  matchGroupPage.verifyMatchGroupDeleted();
});

// Event Details Page specific steps for match group functionality
When('The user expands the first match group', () => {
  eventDetailsPage.expandFirstMatchGroup();
});

When('The user hovers on a match group search', () => {
  eventDetailsPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search', () => {
  eventDetailsPage.clickFirstSearchEditIcon();
});

When('The user enters {string} as the new search name', (newName: string) => {
  eventDetailsPage.enterNewSearchNameInDialog(newName);
});

When('The user clicks {string} in the search dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveInSearchDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    eventDetailsPage.clickDeleteInSearchDialog();
  }
});

Then('The search name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search', () => {
  eventDetailsPage.clickFirstSearchDeleteIcon();
});

Then('The match group search should be deleted successfully', () => {
  eventDetailsPage.verifySearchDeleted();
});

When('The user clicks on the 3 dots button for the first match group', () => {
  eventDetailsPage.clickThreeDotsButtonForFirstMatchGroup();
});

When('The user selects {string}', (option: string) => {
  if (option === 'Rename') {
    eventDetailsPage.selectRenameFromMenu();
  } else if (option === 'Delete') {
    eventDetailsPage.selectDeleteFromMenu();
  }
});

When('The user enters {string} as the new name', (newName: string) => {
  eventDetailsPage.enterNewMatchGroupNameInDialog(newName);
});

When('The user clicks {string} in the match group dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveInMatchGroupDialog();
  }
});

Then('The match group name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal', () => {
  eventDetailsPage.verifyMatchGroupDeleteConfirmationModal();
});

Then('The confirmation modal should display the message {string}', (_message: string) => {
  eventDetailsPage.verifyMatchGroupDeleteConfirmationModal();
});

Then('The modal should have {string} and {string} buttons', (_button1: string, _button2: string) => {
  eventDetailsPage.verifyMatchGroupDeleteConfirmationModal();
});

When('The user clicks {string}', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    eventDetailsPage.clickYesDeleteInMatchGroupDialog();
  }
});

Then('The match group should be deleted successfully', () => {
  eventDetailsPage.verifyMatchGroupDeleted();
});

Then('The user should navigate to match group event details page', () => {
  mainPage.verifyNavigationToEventDetails();
});

Then('The user should see the following match group UI elements:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as Array<{ 'Element Type': string; 'Expected Value': string }>;

  rows.forEach((row) => {
    const elementType = row['Element Type'];
    const expectedValue = row['Expected Value'];

    switch (elementType) {
    case 'Breadcrumb':
      matchGroupPage.verifyBreadcrumb(expectedValue, '');
      break;
    case 'File Name Dropdown':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Export Button':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Left Panel Text':
      if (expectedValue === 'Select a Detection to View Details') {
        matchGroupPage.verifyLeftScreenContent();
      }
      break;
    case 'Tab 1':
    case 'Tab 2':
    case 'Tab 3':
      matchGroupPage.verifyRightScreenTabs();
      break;
    case 'Default Tab':
      if (expectedValue === 'Verified Matches') {
        matchGroupPage.verifyDefaultTab();
      }
      break;
    default:
      throw new Error(`Unknown element type: ${elementType}`);
    }
  });
});
